"use client";

import GalleryUpload from "@/components/gallery-upload";
import AddToCollectionDialog from "@/components/ui/add-to-collection-dialog";
import AlbumFloatingActionBar from "@/components/ui/album-floating-action-bar";
import BulkAddToAlbumDialog from "@/components/ui/bulk-add-to-album-dialog";
import BulkAddToCollectionDialog from "@/components/ui/bulk-add-to-collection-dialog";
import BulkDeleteImagesDialog from "@/components/ui/bulk-delete-images-dialog";
import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import DeleteImageDialog from "@/components/ui/delete-image-dialog";
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from "@/components/ui/dialog";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MasonryGallery from "@/components/ui/masonry-gallery";
import MoveToAlbumDialog from "@/components/ui/move-to-album-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { useAlbum, useUpdateAlbum } from "@/lib/hooks/use-albums";
import {
   useBulkDeleteImages,
   useBulkMoveImagesToAlbum,
   useBulkUpdateImageCollections,
   useDeleteImageCompletely,
   useInfiniteImages,
   useMoveImageToAlbum,
   useSetAlbumCover,
   useUpdateImageCollections,
} from "@/lib/hooks/use-images";
import { downloadImagesAsZip } from "@/lib/utils/download-utils";
import { LockClosedIcon } from "@heroicons/react/24/solid";
import {
   Album,
   ArrowLeft,
   Download,
   Edit,
   Loader2,
   MoreVertical,
   Plus,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useMemo, useState } from "react";

export default function AlbumDetailPage() {
   const params = useParams();
   const albumId = params.id as string;

   // All useState hooks at the top
   const [moveToAlbumDialog, setMoveToAlbumDialog] = useState<{
      open: boolean;
      imageId: string;
   }>({ open: false, imageId: "" });
   const [addToCollectionDialog, setAddToCollectionDialog] = useState<{
      open: boolean;
      imageId: string;
      currentCollectionIds: string[];
   }>({ open: false, imageId: "", currentCollectionIds: [] });
   const [deleteDialog, setDeleteDialog] = useState<{
      open: boolean;
      imageId: string;
      imageName: string;
   }>({ open: false, imageId: "", imageName: "" });
   const [editDialogOpen, setEditDialogOpen] = useState(false);
   const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
   const [dropdownOpen, setDropdownOpen] = useState(false);
   const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());
   const updateAlbumMutation = useUpdateAlbum();
   const [editForm, setEditForm] = useState({
      name: "",
      description: "",
      hasPassword: false,
      password: "",
      coverImageUrl: "",
   });

   // Bulk action dialogs
   const [bulkAddToAlbumDialog, setBulkAddToAlbumDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [bulkAddToCollectionDialog, setBulkAddToCollectionDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [bulkDeleteDialog, setBulkDeleteDialog] = useState<{
      open: boolean;
   }>({ open: false });

   const [isDownloading, setIsDownloading] = useState(false);

   const {
      data: album,
      isLoading: albumLoading,
      error: albumError,
   } = useAlbum(albumId);
   const {
      data: imagesData,
      isLoading: imagesLoading,
      error: imagesError,
      hasNextPage,
      isFetchingNextPage,
      fetchNextPage,
   } = useInfiniteImages({ albumId, limit: 20 });

   // When album loads, pre-fill edit form
   useEffect(() => {
      if (album) {
         setEditForm({
            name: album.name || "",
            description: album.description || "",
            hasPassword: album.hasPassword || false,
            password: album.password || "",
            coverImageUrl: album.coverImageUrl || "",
         });
      }
   }, [album]);

   // Flatten all images from all pages
   const images = useMemo(() => {
      if (!imagesData?.pages) return [];
      return imagesData.pages.flatMap((page) => page.data);
   }, [imagesData?.pages]);

   const totalImages = useMemo(() => {
      if (!imagesData?.pages || imagesData.pages.length === 0) return 0;
      return imagesData.pages[0].pagination.total;
   }, [imagesData?.pages]);

   console.log(album);

   // Mutations
   const deleteImageMutation = useDeleteImageCompletely();
   const moveImageMutation = useMoveImageToAlbum();
   const updateCollectionsMutation = useUpdateImageCollections();
   const setAlbumCoverMutation = useSetAlbumCover();
   const bulkMoveImagesMutation = useBulkMoveImagesToAlbum();
   const bulkUpdateCollectionsMutation = useBulkUpdateImageCollections();
   const bulkDeleteImagesMutation = useBulkDeleteImages();

   if (albumError || imagesError) {
      return (
         <div className="p-8">
            <div className="text-center text-destructive">
               <p>Failed to load album. Please try again.</p>
            </div>
         </div>
      );
   }

   if (albumLoading) {
      return (
         <div className="p-8 space-y-8">
            {/* Header Skeleton */}
            <div className="flex items-center justify-between">
               <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10" />
                  <div className="space-y-2">
                     <Skeleton className="h-8 w-48" />
                     <Skeleton className="h-4 w-32" />
                  </div>
               </div>
               <div className="space-x-3 hidden sm:flex">
                  <Skeleton className="h-10 w-20" />
                  <Skeleton className="h-10 w-32" />
               </div>
            </div>

            {/* Album Info Skeleton */}
            <Card className="border-border/30 py-0">
               <CardContent className="p-4">
                  <div className="flex items-start space-x-6">
                     <Skeleton className="w-32 h-32 rounded-lg" />
                     <div className="flex-1 space-y-3">
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                     </div>
                  </div>
               </CardContent>
            </Card>
         </div>
      );
   }

   if (!album) {
      return (
         <div className="p-8">
            <div className="text-center">
               <p>Album not found.</p>
            </div>
         </div>
      );
   }

   const hasImages = images.length > 0;

   const handleImageSelect = (imageId: string, selected: boolean) => {
      const newSelected = new Set(selectedImages);
      if (selected) {
         newSelected.add(imageId);
      } else {
         newSelected.delete(imageId);
      }
      setSelectedImages(newSelected);
   };

   const handleClearSelection = () => {
      setSelectedImages(new Set());
   };

   const handleBulkMoveToAlbum = () => {
      setBulkAddToAlbumDialog({ open: true });
   };

   const handleBulkAddToCollection = () => {
      setBulkAddToCollectionDialog({ open: true });
   };

   const handleBulkDelete = () => {
      setBulkDeleteDialog({ open: true });
   };

   // Bulk action handlers
   const handleBulkMoveToAlbumAction = async (
      imageIds: string[],
      targetAlbumId: string
   ) => {
      await bulkMoveImagesMutation.mutateAsync({
         imageIds,
         albumId: targetAlbumId,
      });
      setSelectedImages(new Set());
   };

   const handleBulkAddToCollections = async (
      imageIds: string[],
      collectionIds: string[]
   ) => {
      await bulkUpdateCollectionsMutation.mutateAsync({
         imageIds,
         collectionIds,
      });
      setSelectedImages(new Set());
   };

   const handleBulkDeleteImages = async (): Promise<void> => {
      const imageIds = Array.from(selectedImages);
      await bulkDeleteImagesMutation.mutateAsync(imageIds);
      setSelectedImages(new Set());
   };

   const handleSelectAll = () => {
      if (selectedImages.size === images.length) {
         setSelectedImages(new Set());
      } else {
         setSelectedImages(new Set(images.map((img) => img._id!)));
      }
   };

   const handleDownloadAll = async () => {
      if (images.length === 0) return;

      try {
         setIsDownloading(true);
         const imagesForDownload = images.map((img) => ({
            url: img.url,
            name: img.name || `image_${img._id}`,
         }));

         await downloadImagesAsZip(
            imagesForDownload,
            `${album?.name || "album"}_images.zip`
         );
      } catch (error) {
         console.error("Error downloading images:", error);
         // You might want to show a toast notification here
      } finally {
         setIsDownloading(false);
      }
   };

   // Handle image actions
   const handleImageAction = (imageId: string, action: string) => {
      const image = images.find((img) => img._id === imageId);
      if (!image) return;

      switch (action) {
         case "set-cover":
            setAlbumCoverMutation.mutate({ albumId, imageUrl: image.url });
            break;
         case "move-to-album":
            setMoveToAlbumDialog({
               open: true,
               imageId,
            });
            break;
         case "add-to-collection":
            setAddToCollectionDialog({
               open: true,
               imageId,
               currentCollectionIds: image.collectionIds || [],
            });
            break;
         case "delete":
            setDeleteDialog({
               open: true,
               imageId,
               imageName: image.name,
            });
            break;
         default:
            console.log("Unknown action:", action);
      }
   };

   // Handle move to album
   const handleMoveToAlbum = async (
      imageId: string,
      targetAlbumId: string
   ): Promise<void> => {
      await moveImageMutation.mutateAsync({ imageId, albumId: targetAlbumId });
   };

   // Handle add to collections
   const handleAddToCollections = async (
      imageId: string,
      collectionIds: string[]
   ): Promise<void> => {
      await updateCollectionsMutation.mutateAsync({ imageId, collectionIds });
   };

   // Handle delete image
   const handleDeleteImage = async (): Promise<void> => {
      if (deleteDialog.imageId) {
         await deleteImageMutation.mutateAsync(deleteDialog.imageId);
      }
   };

   return (
      <div className="p-6 sm:p-8 space-y-8">
         {/* Header */}
         <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
               <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                     {album.name}
                  </h1>
                  <p className="text-muted-foreground flex items-center space-x-2 font-medium">
                     <span>{album.imageCount} photos</span>
                     {album.hasPassword && (
                        <span className="flex items-center space-x-2 text-sm font-semibold">
                           • Password Locked
                           <LockClosedIcon className="size-3 ml-2 mr-2" />
                        </span>
                     )}
                  </p>
               </div>
            </div>
            {/* Desktop actions */}
            <div className="hidden sm:flex items-center space-x-3">
               <Link href="/admin/albums">
                  <Button variant="outline">
                     <ArrowLeft className="w-4 h-4" />
                     Back to Albums
                  </Button>
               </Link>
               <Button
                  variant="outline"
                  onClick={() => setEditDialogOpen(true)}
               >
                  <Edit className="w-4 h-4" />
                  Edit Album
               </Button>
               {hasImages && (
                  <Button
                     variant="outline"
                     onClick={handleDownloadAll}
                     disabled={isDownloading}
                  >
                     <Download className="w-4 h-4" />
                     {isDownloading ? "Downloading..." : "Download All"}
                  </Button>
               )}
               <Button
                  className="bg-gradient-accent hover:opacity-90"
                  onClick={() => setUploadDialogOpen(true)}
               >
                  <Plus className="w-4 h-4" />
                  Add Images
               </Button>
            </div>
            {/* Mobile actions dropdown */}
            <div className="flex sm:hidden items-center">
               <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
                  <DropdownMenuTrigger asChild>
                     <Button variant="outline" size="icon">
                        <MoreVertical className="w-5 h-5" />
                        <span className="sr-only">Open actions</span>
                     </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="space-y-2">
                     <DropdownMenuItem
                        asChild
                        onClick={() => setDropdownOpen(false)}
                     >
                        <Link href="/admin/albums">
                           <span className="flex items-center">
                              <ArrowLeft className="w-4 h-4 mr-2" /> Back to
                              Albums
                           </span>
                        </Link>
                     </DropdownMenuItem>
                     <DropdownMenuItem
                        onClick={() => {
                           setDropdownOpen(false);
                           setEditDialogOpen(true);
                        }}
                     >
                        <Edit className="w-4 h-4 mr-2" /> Edit Album
                     </DropdownMenuItem>
                     {hasImages && (
                        <DropdownMenuItem
                           onClick={() => {
                              setDropdownOpen(false);
                              handleDownloadAll();
                           }}
                           disabled={isDownloading}
                        >
                           <Download className="w-4 h-4 mr-2" />
                           {isDownloading ? "Downloading..." : "Download All"}
                        </DropdownMenuItem>
                     )}
                     <DropdownMenuItem
                        onClick={() => {
                           setDropdownOpen(false);
                           setUploadDialogOpen(true);
                        }}
                     >
                        <Plus className="w-4 h-4 mr-2" /> Add Images
                     </DropdownMenuItem>
                  </DropdownMenuContent>
               </DropdownMenu>
            </div>
         </div>

         {/* Album Info */}
         <Card className="border-border/50 py-0">
            <CardContent className="p-4">
               <div className="flex flex-col sm:flex-row items-start space-x-6">
                  {/* Album Cover */}
                  <div className="w-full h-48 sm:w-32 sm:h-32 bg-astral-grey-light/50 rounded-lg flex items-center justify-center flex-shrink-0 relative">
                     {album.coverImageUrl ? (
                        <Image
                           src={album.coverImageUrl}
                           alt={album.name}
                           fill
                           className="object-cover aspect-square rounded-lg"
                        />
                     ) : (
                        <Album className="w-12 h-12 text-muted-foreground opacity-50" />
                     )}
                  </div>

                  {/* Album Details */}
                  <div className="flex-1 mt-4 sm:mt-0">
                     <h2 className="text-xl font-semibold text-foreground mb-2">
                        {album.name}
                     </h2>
                     {album.description && (
                        <p className="text-muted-foreground mb-4">
                           {album.description}
                        </p>
                     )}
                     <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>{album.imageCount} photos</span>
                        <span>•</span>
                        {album.hasPassword && (
                           <>
                              <span>Password Protected</span>
                              <span>•</span>
                           </>
                        )}
                        <span>
                           Created{" "}
                           {new Date(album.createdAt).toLocaleDateString()}
                        </span>
                     </div>
                  </div>
               </div>
            </CardContent>
         </Card>

         {/* Images Gallery */}
         <Card className="border-border/50">
            <CardHeader>
               <div className="flex items-center justify-between">
                  <div>
                     <CardTitle className="text-foreground">
                        {hasImages
                           ? `${images.length} of ${totalImages} photos`
                           : `${totalImages} photos`}
                     </CardTitle>
                     <CardDescription>Images in this album</CardDescription>
                  </div>

                  {hasImages && (
                     <Button variant="outline" onClick={handleSelectAll}>
                        {selectedImages.size === images.length
                           ? "Deselect All"
                           : "Select All"}
                     </Button>
                  )}
               </div>
            </CardHeader>
            <CardContent>
               {imagesLoading ? (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                     {Array.from({ length: 12 }).map((_, i) => (
                        <Skeleton
                           key={i}
                           className="aspect-square rounded-lg"
                        />
                     ))}
                  </div>
               ) : hasImages ? (
                  <MasonryGallery
                     images={images}
                     selectedImages={selectedImages}
                     onImageSelect={handleImageSelect}
                     onImageAction={handleImageAction}
                     showSetCover={true}
                     showMoveToAlbum={true}
                     showAddToCollection={true}
                     showCheckboxes={true}
                     hasNextPage={hasNextPage}
                     isFetchingNextPage={isFetchingNextPage}
                     fetchNextPage={fetchNextPage}
                     isLoading={imagesLoading}
                  />
               ) : (
                  <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                     <Album className="w-16 h-16 mb-4 opacity-50" />
                     <h3 className="text-lg font-medium mb-2">
                        No photos in this album
                     </h3>
                     <p className="text-sm mb-4 text-center">
                        Upload images to get started with your album
                     </p>
                     <Button
                        className="bg-gradient-accent hover:opacity-90"
                        onClick={() => setUploadDialogOpen(true)}
                     >
                        <Plus className="w-4 h-4" />
                        Upload Images
                     </Button>
                  </div>
               )}
            </CardContent>
         </Card>

         {/* Floating Action Bar */}
         <AlbumFloatingActionBar
            selectedCount={selectedImages.size}
            onClearSelection={handleClearSelection}
            onMoveToAlbum={handleBulkMoveToAlbum}
            onAddToCollection={handleBulkAddToCollection}
            onDelete={handleBulkDelete}
         />

         {/* Dialogs */}
         <MoveToAlbumDialog
            open={moveToAlbumDialog.open}
            onOpenChange={(open) => setMoveToAlbumDialog({ open, imageId: "" })}
            imageId={moveToAlbumDialog.imageId}
            currentAlbumId={albumId}
            onMove={handleMoveToAlbum}
         />

         <AddToCollectionDialog
            open={addToCollectionDialog.open}
            onOpenChange={(open) =>
               setAddToCollectionDialog({
                  open,
                  imageId: "",
                  currentCollectionIds: [],
               })
            }
            imageId={addToCollectionDialog.imageId}
            currentCollectionIds={addToCollectionDialog.currentCollectionIds}
            onAddToCollections={handleAddToCollections}
         />

         <DeleteImageDialog
            open={deleteDialog.open}
            onOpenChange={(open) =>
               setDeleteDialog({ open, imageId: "", imageName: "" })
            }
            imageName={deleteDialog.imageName}
            onConfirm={handleDeleteImage}
            isDeleting={deleteImageMutation.isPending}
         />

         {/* Edit Album Dialog */}
         <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
            <DialogContent className="sm:max-w-[425px]">
               <DialogHeader>
                  <DialogTitle>Edit Album</DialogTitle>
                  <DialogDescription>
                     Edit album details below.
                  </DialogDescription>
               </DialogHeader>
               <form
                  onSubmit={(e) => {
                     e.preventDefault();
                     updateAlbumMutation.mutate(
                        {
                           id: albumId,
                           input: {
                              name: editForm.name,
                              description: editForm.description,
                              hasPassword: editForm.hasPassword,
                              password: editForm.hasPassword
                                 ? editForm.password
                                 : undefined,
                              coverImageUrl: editForm.coverImageUrl,
                           },
                        },
                        {
                           onSuccess: () => setEditDialogOpen(false),
                        }
                     );
                  }}
                  className="space-y-5"
               >
                  <div className="space-y-3">
                     <Label htmlFor="edit-name">Album Name *</Label>
                     <Input
                        id="edit-name"
                        value={editForm.name}
                        onChange={(e) =>
                           setEditForm((f) => ({ ...f, name: e.target.value }))
                        }
                        required
                     />
                  </div>
                  <div className="space-y-3">
                     <Label htmlFor="edit-description">Description</Label>
                     <Textarea
                        id="edit-description"
                        value={editForm.description}
                        onChange={(e) =>
                           setEditForm((f) => ({
                              ...f,
                              description: e.target.value,
                           }))
                        }
                        rows={3}
                     />
                  </div>
                  <div className="flex items-center space-x-2">
                     <Checkbox
                        id="edit-hasPassword"
                        checked={editForm.hasPassword}
                        onCheckedChange={(checked: boolean) =>
                           setEditForm((f) => ({ ...f, hasPassword: checked }))
                        }
                     />
                     <Label
                        id="edit-hasPassword"
                        className="text-sm font-normal"
                     >
                        Add password
                     </Label>
                  </div>
                  {editForm.hasPassword && (
                     <div className="space-y-3">
                        <Label htmlFor="edit-password">Password *</Label>
                        <Input
                           id="edit-password"
                           type="text"
                           value={editForm.password}
                           onChange={(e) =>
                              setEditForm((f) => ({
                                 ...f,
                                 password: e.target.value,
                              }))
                           }
                           required
                        />
                     </div>
                  )}
                  <div className="space-y-3">
                     <Label htmlFor="edit-coverImageUrl">Cover Image URL</Label>
                     <Input
                        id="edit-coverImageUrl"
                        value={editForm.coverImageUrl}
                        onChange={(e) =>
                           setEditForm((f) => ({
                              ...f,
                              coverImageUrl: e.target.value,
                           }))
                        }
                     />
                  </div>
                  <DialogFooter>
                     <Button
                        type="button"
                        variant="outline"
                        onClick={() => setEditDialogOpen(false)}
                        disabled={updateAlbumMutation.isPending}
                     >
                        Cancel
                     </Button>
                     <Button
                        type="submit"
                        disabled={updateAlbumMutation.isPending}
                        className="bg-gradient-accent hover:opacity-90"
                     >
                        {updateAlbumMutation.isPending ? (
                           <>
                              <Loader2 className="w-4 h-4 animate-spin" />{" "}
                              Saving...
                           </>
                        ) : (
                           "Save Changes"
                        )}
                     </Button>
                  </DialogFooter>
               </form>
            </DialogContent>
         </Dialog>

         {/* Upload Image Dialog */}
         <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
            <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
               <DialogHeader>
                  <DialogTitle>Upload Images to Album</DialogTitle>
               </DialogHeader>
               <GalleryUpload
                  options={{ albumId }}
                  title="Upload Images to Album"
                  onUploadComplete={() => setUploadDialogOpen(false)}
               />
            </DialogContent>
         </Dialog>

         {/* Bulk Action Dialogs */}
         <BulkAddToAlbumDialog
            open={bulkAddToAlbumDialog.open}
            onOpenChange={(open) => setBulkAddToAlbumDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onMove={handleBulkMoveToAlbumAction}
         />

         <BulkAddToCollectionDialog
            open={bulkAddToCollectionDialog.open}
            onOpenChange={(open) => setBulkAddToCollectionDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onAddToCollections={handleBulkAddToCollections}
         />

         <BulkDeleteImagesDialog
            open={bulkDeleteDialog.open}
            onOpenChange={(open) => setBulkDeleteDialog({ open })}
            imageIds={Array.from(selectedImages)}
            onConfirm={handleBulkDeleteImages}
            isDeleting={bulkDeleteImagesMutation.isPending}
         />
      </div>
   );
}
