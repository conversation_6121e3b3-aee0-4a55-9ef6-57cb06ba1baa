"use client";

import { generateStructuredData } from "./meta-tags";

interface SEOHeadProps {
   structuredData?: Array<{
      type: string;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      data?: any;
   }>;
   additionalMeta?: Array<{
      name?: string;
      property?: string;
      content: string;
   }>;
   preconnect?: string[];
   prefetch?: string[];
}

/**
 * Client-side SEO component for additional meta tags and structured data
 * Use this in addition to the server-side metadata generation
 */
export function SEOHead({
   structuredData = [],
   additionalMeta = [],
   preconnect = [],
   prefetch = [],
}: SEOHeadProps) {
   return (
      <>
         {/* Additional meta tags */}
         {additionalMeta.map((meta, index) => (
            <meta
               key={index}
               {...(meta.name && { name: meta.name })}
               {...(meta.property && { property: meta.property })}
               content={meta.content}
            />
         ))}

         {/* Preconnect to external domains */}
         {preconnect.map((url, index) => (
            <link key={`preconnect-${index}`} rel="preconnect" href={url} />
         ))}

         {/* Prefetch important resources */}
         {prefetch.map((url, index) => (
            <link key={`prefetch-${index}`} rel="prefetch" href={url} />
         ))}

         {/* Structured Data */}
         {structuredData.map((item, index) => (
            <script
               key={`structured-data-${index}`}
               type="application/ld+json"
               dangerouslySetInnerHTML={{
                  __html: JSON.stringify(
                     generateStructuredData(item.type, item.data)
                  ),
               }}
            />
         ))}

         {/* Default structured data for all pages */}
         <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
               __html: JSON.stringify(generateStructuredData("Organization")),
            }}
         />

         <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
               __html: JSON.stringify(generateStructuredData("WebSite")),
            }}
         />
      </>
   );
}

/**
 * Predefined SEO configurations for common page types
 */
export const seoConfigs = {
   homepage: {
      structuredData: [
         { type: "LocalBusiness" },
         {
            type: "Service",
            data: {
               serviceType: "Photography and Videography Services",
               description:
                  "Professional photography and videography services including weddings, pre-wedding shoots, pregnancy photography, child dedication, 360 video booth rental, and dry ice machine rental.",
            },
         },
      ],
      additionalMeta: [
         { name: "geo.region", content: "GB" },
         { name: "geo.placename", content: "United Kingdom" },
         { name: "ICBM", content: "51.5074, -0.1278" },
         {
            property: "business:contact_data:street_address",
            content: "London, UK",
         },
         { property: "business:contact_data:locality", content: "London" },
         { property: "business:contact_data:region", content: "England" },
         { property: "business:contact_data:postal_code", content: "" },
         {
            property: "business:contact_data:country_name",
            content: "United Kingdom",
         },
      ],
      preconnect: ["https://fonts.googleapis.com", "https://fonts.gstatic.com"],
   },

   services: {
      structuredData: [
         {
            type: "Service",
            data: {
               serviceType: "Professional Photography Services",
               description:
                  "Comprehensive photography and videography services for weddings, events, and special occasions",
            },
         },
      ],
      additionalMeta: [
         { name: "service.type", content: "Photography and Videography" },
         { name: "service.area", content: "United Kingdom" },
      ],
   },

   portfolio: {
      structuredData: [
         {
            type: "CreativeWork",
            data: {
               "@type": "Photograph",
               name: "Astral Studios Portfolio",
               description:
                  "Professional photography portfolio showcasing weddings, events, and special occasions",
               creator: {
                  "@type": "Organization",
                  name: "Astral Studios",
               },
            },
         },
      ],
   },

   contact: {
      structuredData: [
         {
            type: "ContactPage",
            data: {
               name: "Contact Astral Studios",
               description:
                  "Get in touch with Astral Studios for photography and videography services",
               mainEntity: {
                  "@type": "Organization",
                  name: "Astral Studios",
                  telephone: "+447886161245",
                  email: "<EMAIL>",
               },
            },
         },
      ],
      additionalMeta: [
         { name: "contact.email", content: "<EMAIL>" },
         { name: "contact.phone", content: "+447886161245" },
      ],
   },
};

/**
 * Service-specific SEO configurations
 */
export const serviceSEOConfigs = {
   wedding: {
      structuredData: [
         {
            type: "Service",
            data: {
               serviceType: "Wedding Photography",
               description:
                  "Professional wedding photography services capturing your special day",
               offers: {
                  "@type": "Offer",
                  name: "Wedding Photography Package",
                  description: "Full day wedding coverage with online gallery",
                  priceCurrency: "GBP",
               },
            },
         },
      ],
   },

   "pre-wedding": {
      structuredData: [
         {
            type: "Service",
            data: {
               serviceType: "Pre-Wedding Photography",
               description: "Engagement and pre-wedding photography sessions",
               offers: {
                  "@type": "Offer",
                  name: "Pre-Wedding Photography Session",
                  description:
                     "Romantic couple photography with location scouting",
                  priceCurrency: "GBP",
               },
            },
         },
      ],
   },

   pregnancy: {
      structuredData: [
         {
            type: "Service",
            data: {
               serviceType: "Maternity Photography",
               description: "Professional pregnancy and maternity photography",
               offers: {
                  "@type": "Offer",
                  name: "Maternity Photography Session",
                  description:
                     "Beautiful pregnancy portraits in studio or outdoor locations",
                  priceCurrency: "GBP",
               },
            },
         },
      ],
   },
};
